// NerdAlert Background Script
// Manages timing and settings for the overlay system

class NerdAlertBackground {
    constructor() {
        this.defaultSettings = {
            interval: 30, // minutes
            difficulty: 'all',
            category: 'all',
            enabled: true,
            lastShown: 0,
            autoHide: true,
            autoHideDelay: 30 // seconds
        };
        
        this.init();
    }

    init() {
        // Initialize settings on install
        chrome.runtime.onInstalled.addListener(() => {
            this.initializeSettings();
        });

        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
        });

        // Handle tab updates to inject content script if needed
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                // Only process valid URLs for content script injection
                if (this.isValidTabForContentScript(tab.url)) {
                    this.checkAndInjectContentScript(tabId, tab.url);
                }
            }
        });
    }

    async initializeSettings() {
        try {
            const result = await chrome.storage.sync.get(['nerdAlertSettings']);
            if (!result.nerdAlertSettings) {
                await chrome.storage.sync.set({
                    nerdAlertSettings: this.defaultSettings
                });
                // Only log on actual initialization, not every time
            }
        } catch (error) {
            console.error('NerdAlert: Failed to initialize settings:', error);
        }
    }

    async handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'getSettings':
                const settings = await this.getSettings();
                sendResponse(settings);
                break;

            case 'updateSettings':
                await this.updateSettings(request.settings);
                // Notify all content scripts about settings change
                this.broadcastSettingsUpdate(request.settings);
                sendResponse({ success: true });
                break;

            case 'showAlert':
                this.showAlertOnActiveTab();
                break;

            case 'openSettings':
                chrome.runtime.openOptionsPage();
                break;

            case 'testAlert':
                console.log('NerdAlert: Test alert requested');
                this.showAlertOnActiveTab();
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ error: 'Unknown action' });
        }
    }

    async getSettings() {
        try {
            const result = await chrome.storage.sync.get(['nerdAlertSettings']);
            return result.nerdAlertSettings || this.defaultSettings;
        } catch (error) {
            console.error('NerdAlert: Failed to get settings:', error);
            return this.defaultSettings;
        }
    }

    async updateSettings(newSettings) {
        try {
            const currentSettings = await this.getSettings();
            const updatedSettings = { ...currentSettings, ...newSettings };
            await chrome.storage.sync.set({ nerdAlertSettings: updatedSettings });
            console.log('NerdAlert: Settings updated:', updatedSettings);
        } catch (error) {
            console.error('NerdAlert: Failed to update settings:', error);
        }
    }

    async broadcastSettingsUpdate(settings) {
        try {
            const tabs = await chrome.tabs.query({});
            for (const tab of tabs) {
                if (this.isValidTabForContentScript(tab.url)) {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'updateSettings',
                        settings: settings
                    }).catch(() => {
                        // Ignore errors for tabs without content script
                    });
                }
            }
        } catch (error) {
            console.error('NerdAlert: Failed to broadcast settings:', error);
        }
    }

    async showAlertOnActiveTab() {
        try {
            const [activeTab] = await chrome.tabs.query({
                active: true,
                currentWindow: true
            });

            console.log('NerdAlert: Active tab:', activeTab?.url);

            if (activeTab && this.isValidTabForContentScript(activeTab.url)) {
                console.log('NerdAlert: Sending showAlert message to tab', activeTab.id);
                chrome.tabs.sendMessage(activeTab.id, {
                    action: 'showAlert'
                }).then((response) => {
                    console.log('NerdAlert: Alert message response:', response);
                }).catch((error) => {
                    console.log('NerdAlert: Content script not ready, injecting...', error);
                    this.injectContentScript(activeTab.id);
                });
            } else {
                console.log('NerdAlert: Invalid tab for content script:', activeTab?.url);
            }
        } catch (error) {
            console.error('NerdAlert: Failed to show alert on active tab:', error);
        }
    }

    isValidTabForContentScript(url) {
        if (!url) return false;
        
        const invalidProtocols = [
            'chrome://',
            'chrome-extension://',
            'edge://',
            'about:',
            'moz-extension://',
            'file://',
            'chrome-search://',
            'chrome-native://',
            'devtools://'
        ];
        
        // Only allow http and https URLs
        return url.startsWith('http://') || url.startsWith('https://');
    }

    async checkAndInjectContentScript(tabId, url) {
        if (!this.isValidTabForContentScript(url)) return;

        try {
            // Check if content script is already injected
            const response = await chrome.tabs.sendMessage(tabId, { 
                action: 'ping' 
            });
        } catch (error) {
            // Content script not injected, inject it
            this.injectContentScript(tabId);
        }
    }

    async injectContentScript(tabId) {
        try {
            // First check if the tab still exists and is valid
            const tab = await chrome.tabs.get(tabId);
            if (!this.isValidTabForContentScript(tab.url)) {
                console.log('NerdAlert: Skipping injection for invalid URL:', tab.url);
                return;
            }

            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['js/content.js']
            });

            await chrome.scripting.insertCSS({
                target: { tabId: tabId },
                files: ['css/overlay.css']
            });

            console.log('NerdAlert: Content script injected into tab', tabId);
        } catch (error) {
            console.error('NerdAlert: Failed to inject content script:', error);
        }
    }
}

// Initialize background script
new NerdAlertBackground();