{"questions": [{"id": 1, "category": "Big-O", "question": "What's the time complexity of binary search?", "answer": "O(log n) - We eliminate half the search space with each comparison.", "difficulty": "easy"}, {"id": 2, "category": "Data Structures", "question": "What's the main advantage of a hash table?", "answer": "O(1) average-case lookup time for key-value operations.", "difficulty": "easy"}, {"id": 3, "category": "Algorithms", "question": "Which sorting algorithm is stable and has O(n log n) worst-case?", "answer": "Merge sort - it maintains relative order of equal elements.", "difficulty": "medium"}, {"id": 4, "category": "Big-O", "question": "What's the space complexity of quicksort?", "answer": "O(log n) average case due to recursion stack, O(n) worst case.", "difficulty": "medium"}, {"id": 5, "category": "Data Structures", "question": "When would you use a stack over a queue?", "answer": "When you need LIFO behavior: undo operations, function calls, expression parsing.", "difficulty": "easy"}, {"id": 6, "category": "Programming", "question": "What's the difference between '==' and '===' in JavaScript?", "answer": "'==' does type coercion, '===' checks both value and type without conversion.", "difficulty": "easy"}, {"id": 7, "category": "Big-O", "question": "What's the time complexity of inserting into a balanced BST?", "answer": "O(log n) - tree height is logarithmic in a balanced tree.", "difficulty": "medium"}, {"id": 8, "category": "Algorithms", "question": "What's the key insight behind dynamic programming?", "answer": "Optimal substructure + overlapping subproblems = memoize to avoid recomputation.", "difficulty": "hard"}, {"id": 9, "category": "Data Structures", "question": "What's the difference between an array and a linked list?", "answer": "Arrays: O(1) random access, contiguous memory. Lists: O(1) insertion/deletion, non-contiguous.", "difficulty": "easy"}, {"id": 10, "category": "Programming", "question": "What's a closure in programming?", "answer": "A function that captures variables from its outer scope, even after the outer function returns.", "difficulty": "medium"}, {"id": 11, "category": "Big-O", "question": "What's the average time complexity of quicksort?", "answer": "O(n log n) - with good pivot selection, we get balanced partitions.", "difficulty": "medium"}, {"id": 12, "category": "Data Structures", "question": "What's a heap used for?", "answer": "Priority queues, heap sort, and finding min/max elements efficiently.", "difficulty": "medium"}, {"id": 13, "category": "Algorithms", "question": "What's the difference between BFS and DFS?", "answer": "BFS explores level by level (queue), DFS goes deep first (stack/recursion).", "difficulty": "easy"}, {"id": 14, "category": "Programming", "question": "What's the difference between pass-by-value and pass-by-reference?", "answer": "Pass-by-value copies the value, pass-by-reference passes the memory address.", "difficulty": "medium"}, {"id": 15, "category": "Big-O", "question": "What's the time complexity of accessing an element in a hash table?", "answer": "O(1) average case, O(n) worst case (when all keys hash to same bucket).", "difficulty": "medium"}, {"id": 16, "category": "Data Structures", "question": "When would you use a trie?", "answer": "For prefix-based operations: autocomplete, spell checkers, IP routing.", "difficulty": "hard"}, {"id": 17, "category": "Algorithms", "question": "What's the key principle of greedy algorithms?", "answer": "Make the locally optimal choice at each step, hoping for a global optimum.", "difficulty": "medium"}, {"id": 18, "category": "Programming", "question": "What's the difference between synchronous and asynchronous code?", "answer": "Sync blocks execution until complete, async allows other code to run while waiting.", "difficulty": "easy"}, {"id": 19, "category": "Big-O", "question": "What's the space complexity of merge sort?", "answer": "O(n) - requires additional space for the temporary arrays during merging.", "difficulty": "medium"}, {"id": 20, "category": "Data Structures", "question": "What's the main advantage of a B-tree over a binary tree?", "answer": "Better for disk storage: fewer levels, more keys per node, reduces I/O operations.", "difficulty": "hard"}, {"id": 21, "category": "Programming", "question": "What's polymorphism in OOP?", "answer": "The ability of objects of different types to be treated as instances of the same type.", "difficulty": "medium"}, {"id": 22, "category": "Algorithms", "question": "What's the time complexity of <PERSON><PERSON><PERSON>'s algorithm?", "answer": "O((V + E) log V) with a binary heap, where V is vertices and E is edges.", "difficulty": "hard"}, {"id": 23, "category": "Big-O", "question": "What does O(1) mean?", "answer": "Constant time - the operation takes the same time regardless of input size.", "difficulty": "easy"}, {"id": 24, "category": "Data Structures", "question": "What's the difference between a stack and a heap (memory)?", "answer": "Stack: automatic, LIFO, function calls. Heap: manual allocation, dynamic memory.", "difficulty": "medium"}, {"id": 25, "category": "Programming", "question": "What's the difference between compilation and interpretation?", "answer": "Compilation translates to machine code before execution, interpretation executes line by line.", "difficulty": "easy"}, {"id": 26, "category": "Algorithms", "question": "What's memoization?", "answer": "Caching function results to avoid redundant calculations in recursive algorithms.", "difficulty": "medium"}, {"id": 27, "category": "Big-O", "question": "What's the worst-case time complexity of quicksort?", "answer": "O(n²) - when the pivot is always the smallest or largest element.", "difficulty": "medium"}, {"id": 28, "category": "Data Structures", "question": "What's a graph?", "answer": "A collection of vertices (nodes) connected by edges, used to model relationships.", "difficulty": "easy"}, {"id": 29, "category": "Programming", "question": "What's the difference between abstract classes and interfaces?", "answer": "Abstract classes can have implementation, interfaces define contracts (methods to implement).", "difficulty": "medium"}, {"id": 30, "category": "Algorithms", "question": "What's the purpose of hashing?", "answer": "To map data to fixed-size values for fast lookup, data integrity, or distribution.", "difficulty": "easy"}, {"id": 31, "category": "Big-O", "question": "What's the time complexity of bubble sort?", "answer": "O(n²) in worst and average case, O(n) best case (already sorted).", "difficulty": "easy"}, {"id": 32, "category": "Data Structures", "question": "What's a red-black tree?", "answer": "A self-balancing BST with color properties ensuring O(log n) operations.", "difficulty": "hard"}, {"id": 33, "category": "Programming", "question": "What's garbage collection?", "answer": "Automatic memory management that frees unused objects to prevent memory leaks.", "difficulty": "medium"}, {"id": 34, "category": "Algorithms", "question": "What's the difference between divide-and-conquer and dynamic programming?", "answer": "D&C solves independent subproblems, DP solves overlapping subproblems with memoization.", "difficulty": "hard"}, {"id": 35, "category": "Big-O", "question": "What does O(n log n) typically indicate?", "answer": "Efficient sorting algorithms like merge sort, heap sort, or optimal comparison sorts.", "difficulty": "medium"}, {"id": 36, "category": "Data Structures", "question": "What's the difference between a min-heap and max-heap?", "answer": "Min-heap: parent ≤ children (root is minimum). Max-heap: parent ≥ children (root is maximum).", "difficulty": "easy"}, {"id": 37, "category": "Programming", "question": "What's a race condition?", "answer": "When multiple threads access shared data simultaneously, causing unpredictable results.", "difficulty": "medium"}, {"id": 38, "category": "Algorithms", "question": "What's the traveling salesman problem?", "answer": "Finding the shortest route visiting all cities exactly once - NP-hard optimization problem.", "difficulty": "hard"}, {"id": 39, "category": "Big-O", "question": "What's the space complexity of an iterative algorithm vs recursive?", "answer": "Iterative: O(1) typically. Recursive: O(depth) due to call stack.", "difficulty": "medium"}, {"id": 40, "category": "Data Structures", "question": "When would you use a deque (double-ended queue)?", "answer": "When you need efficient insertion/deletion at both ends, like sliding window problems.", "difficulty": "medium"}, {"id": 41, "category": "Programming", "question": "What's the difference between deep copy and shallow copy?", "answer": "Shallow copy copies references, deep copy creates new objects for nested structures.", "difficulty": "medium"}, {"id": 42, "category": "Algorithms", "question": "What's binary search's main requirement?", "answer": "The data must be sorted - binary search relies on the sorted property to eliminate half the search space.", "difficulty": "easy"}, {"id": 43, "category": "Big-O", "question": "What's the time complexity of finding an element in an unsorted array?", "answer": "O(n) - worst case requires checking every element (linear search).", "difficulty": "easy"}, {"id": 44, "category": "Data Structures", "question": "What's a bloom filter?", "answer": "A probabilistic data structure for membership testing - can have false positives, never false negatives.", "difficulty": "hard"}, {"id": 45, "category": "Programming", "question": "What's the difference between static and dynamic typing?", "answer": "Static: types checked at compile time. Dynamic: types checked at runtime.", "difficulty": "easy"}, {"id": 46, "category": "Algorithms", "question": "What's the knapsack problem?", "answer": "Optimization problem: maximize value of items in a knapsack with weight constraint.", "difficulty": "hard"}, {"id": 47, "category": "Big-O", "question": "What's the time complexity of heap operations (insert, delete, peek)?", "answer": "Insert: O(log n), Delete: O(log n), Peek: O(1).", "difficulty": "medium"}, {"id": 48, "category": "Data Structures", "question": "What's the difference between a tree and a graph?", "answer": "Trees are acyclic connected graphs with exactly n-1 edges for n nodes.", "difficulty": "medium"}, {"id": 49, "category": "Programming", "question": "What's a deadlock?", "answer": "When two or more threads wait indefinitely for each other to release resources.", "difficulty": "medium"}, {"id": 50, "category": "Algorithms", "question": "What's the difference between stable and unstable sorting?", "answer": "Stable sorting preserves the relative order of equal elements, unstable doesn't guarantee this.", "difficulty": "medium"}, {"id": 51, "category": "Big-O", "question": "What's the time complexity of matrix multiplication?", "answer": "O(n³) for standard algorithm, O(n^2.807) for <PERSON><PERSON><PERSON>'s algorithm.", "difficulty": "hard"}, {"id": 52, "category": "Data Structures", "question": "What's a segment tree used for?", "answer": "Range queries and updates in O(log n) time, like finding sum/min/max in a range.", "difficulty": "hard"}, {"id": 53, "category": "Programming", "question": "What's the difference between composition and inheritance?", "answer": "Composition: 'has-a' relationship. Inheritance: 'is-a' relationship. Composition is often preferred.", "difficulty": "medium"}, {"id": 54, "category": "Algorithms", "question": "What's A* search algorithm?", "answer": "Best-first search using heuristic function f(n) = g(n) + h(n) for pathfinding.", "difficulty": "hard"}, {"id": 55, "category": "Big-O", "question": "What's the space complexity of DFS vs BFS?", "answer": "DFS: O(h) where h is height. BFS: O(w) where w is maximum width.", "difficulty": "medium"}, {"id": 56, "category": "Data Structures", "question": "What's a union-find (disjoint set) data structure?", "answer": "Tracks disjoint sets with union and find operations, used in <PERSON><PERSON><PERSON>'s algorithm.", "difficulty": "hard"}, {"id": 57, "category": "Programming", "question": "What's the difference between process and thread?", "answer": "Process: independent memory space. Thread: shared memory within a process.", "difficulty": "medium"}, {"id": 58, "category": "Algorithms", "question": "What's the purpose of the two-pointer technique?", "answer": "Efficiently solve array/string problems by maintaining two pointers moving in specific patterns.", "difficulty": "medium"}, {"id": 59, "category": "Big-O", "question": "What's the time complexity of building a heap from an array?", "answer": "O(n) using bottom-up heapify, not O(n log n) as might be expected.", "difficulty": "hard"}, {"id": 60, "category": "Data Structures", "question": "What's the difference between ArrayList and LinkedList?", "answer": "ArrayList: O(1) access, O(n) insertion. LinkedList: O(n) access, O(1) insertion at known position.", "difficulty": "medium"}, {"id": 61, "category": "Programming", "question": "What's immutability in programming?", "answer": "Objects that cannot be modified after creation - creates new objects instead of changing existing ones.", "difficulty": "easy"}, {"id": 62, "category": "Algorithms", "question": "What's the sliding window technique?", "answer": "Maintain a window of elements and slide it to solve subarray/substring problems efficiently.", "difficulty": "medium"}, {"id": 63, "category": "Big-O", "question": "What's the time complexity of string concatenation in a loop?", "answer": "O(n²) in languages with immutable strings due to creating new strings each time.", "difficulty": "medium"}, {"id": 64, "category": "Data Structures", "question": "What's a circular buffer?", "answer": "Fixed-size buffer that wraps around when full, useful for streaming data or caching.", "difficulty": "medium"}, {"id": 65, "category": "Programming", "question": "What's the difference between stack overflow and heap overflow?", "answer": "Stack overflow: too many function calls. Heap overflow: running out of dynamic memory.", "difficulty": "medium"}, {"id": 66, "category": "Algorithms", "question": "What's backtracking?", "answer": "Algorithmic approach that abandons partial solutions when they can't lead to a valid solution.", "difficulty": "medium"}, {"id": 67, "category": "Big-O", "question": "What's the time complexity of checking if a number is prime?", "answer": "O(√n) - only need to check divisors up to the square root.", "difficulty": "medium"}, {"id": 68, "category": "Data Structures", "question": "What's a skip list?", "answer": "Probabilistic data structure that allows O(log n) search in a sorted linked list.", "difficulty": "hard"}, {"id": 69, "category": "Programming", "question": "What's the difference between mutex and semaphore?", "answer": "Mutex: binary lock for one resource. Semaphore: counter for multiple identical resources.", "difficulty": "hard"}, {"id": 70, "category": "Algorithms", "question": "What's the Boyer-Moore majority vote algorithm?", "answer": "Finds majority element (>n/2 occurrences) in O(n) time and O(1) space.", "difficulty": "hard"}, {"id": 71, "category": "Big-O", "question": "What's the time complexity of the Euclidean algorithm for GCD?", "answer": "O(log(min(a,b))) - very efficient for finding greatest common divisor.", "difficulty": "medium"}, {"id": 72, "category": "Data Structures", "question": "What's a fenwick tree (binary indexed tree)?", "answer": "Data structure for efficient prefix sum queries and updates in O(log n).", "difficulty": "hard"}, {"id": 73, "category": "Programming", "question": "What's the difference between compilation and transpilation?", "answer": "Compilation: high-level to machine code. Transpilation: high-level to another high-level language.", "difficulty": "medium"}, {"id": 74, "category": "Algorithms", "question": "What's the difference between <PERSON><PERSON>'s and <PERSON><PERSON><PERSON>'s algorithms?", "answer": "Both find MST<PERSON>'s grows tree from vertex, <PERSON><PERSON><PERSON>'s sorts edges and uses union-find.", "difficulty": "hard"}, {"id": 75, "category": "Big-O", "question": "What's the time complexity of radix sort?", "answer": "O(d × (n + k)) where d is digits, n is numbers, k is range of each digit.", "difficulty": "hard"}, {"id": 76, "category": "Data Structures", "question": "What's the difference between a min-heap and a priority queue?", "answer": "Priority queue is an abstract data type, min-heap is a common implementation of it.", "difficulty": "medium"}, {"id": 77, "category": "Programming", "question": "What's tail recursion?", "answer": "Recursion where the recursive call is the last operation - can be optimized to iteration.", "difficulty": "medium"}, {"id": 78, "category": "Algorithms", "question": "What's the difference between BFS and <PERSON><PERSON><PERSON>'s algorithm?", "answer": "BFS works on unweighted graphs, <PERSON><PERSON><PERSON>'s handles weighted graphs with non-negative weights.", "difficulty": "medium"}, {"id": 79, "category": "Big-O", "question": "What's the time complexity of counting sort?", "answer": "O(n + k) where n is elements and k is the range of input values.", "difficulty": "medium"}, {"id": 80, "category": "Data Structures", "question": "What's a rope data structure?", "answer": "Tree structure for efficiently storing and manipulating very long strings.", "difficulty": "hard"}, {"id": 81, "category": "Programming", "question": "What's the difference between functional and imperative programming?", "answer": "Functional: focuses on functions and immutability. Imperative: focuses on changing state with statements.", "difficulty": "medium"}, {"id": 82, "category": "Algorithms", "question": "What's the <PERSON><PERSON>'s algorithm used for?", "answer": "Finding all palindromic substrings in linear O(n) time.", "difficulty": "hard"}, {"id": 83, "category": "Big-O", "question": "What's the time complexity of the sieve of Eratosthenes?", "answer": "O(n log log n) for finding all primes up to n.", "difficulty": "medium"}, {"id": 84, "category": "Data Structures", "question": "What's a persistent data structure?", "answer": "Data structure that preserves previous versions when modified, enabling time travel.", "difficulty": "hard"}, {"id": 85, "category": "Programming", "question": "What's the difference between early binding and late binding?", "answer": "Early binding: resolved at compile time. Late binding: resolved at runtime (polymorphism).", "difficulty": "medium"}, {"id": 86, "category": "Algorithms", "question": "What's the KMP (<PERSON><PERSON><PERSON><PERSON>) algorithm?", "answer": "String matching algorithm that avoids redundant comparisons using failure function, O(n+m).", "difficulty": "hard"}, {"id": 87, "category": "Big-O", "question": "What's the time complexity of finding strongly connected components?", "answer": "O(V + E) using algorithms like <PERSON><PERSON><PERSON>'s or <PERSON><PERSON><PERSON><PERSON>'s.", "difficulty": "hard"}, {"id": 88, "category": "Data Structures", "question": "What's a van Emde Boas tree?", "answer": "Data structure supporting operations in O(log log u) time where u is universe size.", "difficulty": "hard"}, {"id": 89, "category": "Programming", "question": "What's the difference between cohesion and coupling?", "answer": "Cohesion: how related elements within a module are. Coupling: how dependent modules are on each other.", "difficulty": "medium"}, {"id": 90, "category": "Algorithms", "question": "What's the difference between online and offline algorithms?", "answer": "Online: processes input as it arrives. Offline: has access to entire input before processing.", "difficulty": "medium"}, {"id": 91, "category": "Big-O", "question": "What's the time complexity of topological sorting?", "answer": "O(V + E) using <PERSON><PERSON> or <PERSON>'s algorithm on a directed acyclic graph.", "difficulty": "medium"}, {"id": 92, "category": "Data Structures", "question": "What's a suffix array?", "answer": "Sorted array of all suffixes of a string, useful for pattern matching and string analysis.", "difficulty": "hard"}, {"id": 93, "category": "Programming", "question": "What's the difference between monolithic and microservices architecture?", "answer": "Monolithic: single deployable unit. Microservices: multiple independent services communicating over network.", "difficulty": "medium"}, {"id": 94, "category": "Algorithms", "question": "What's the <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm?", "answer": "Finds maximum flow in a flow network using augmenting paths.", "difficulty": "hard"}, {"id": 95, "category": "Big-O", "question": "What's the time complexity of the fast Fourier transform (FFT)?", "answer": "O(n log n) compared to O(n²) for naive discrete Fourier transform.", "difficulty": "hard"}, {"id": 96, "category": "Data Structures", "question": "What's a treap?", "answer": "Randomized binary search tree that maintains both BST and heap properties.", "difficulty": "hard"}, {"id": 97, "category": "Programming", "question": "What's the difference between optimistic and pessimistic locking?", "answer": "Optimistic: assumes no conflicts, checks at commit. Pessimistic: locks resources upfront.", "difficulty": "medium"}, {"id": 98, "category": "Algorithms", "question": "What's the Z algorithm?", "answer": "Linear time string matching algorithm that computes Z-array for pattern matching.", "difficulty": "hard"}, {"id": 99, "category": "Big-O", "question": "What's the time complexity of matrix chain multiplication optimization?", "answer": "O(n³) using dynamic programming to find optimal parenthesization.", "difficulty": "hard"}, {"id": 100, "category": "Data Structures", "question": "What's the difference between B-tree and B+ tree?", "answer": "B+ tree: data only in leaves, internal nodes for navigation. Better for range queries.", "difficulty": "hard"}, {"id": 1, "category": "Big-O", "question": "What's the time complexity of binary search?", "answer": "O(log n) - We eliminate half the search space with each comparison.", "difficulty": "easy"}, {"id": 2, "category": "Big-O", "question": "What is the time complexity of inserting an element into a hash table?", "answer": "O(1) on average, but O(n) in the worst case due to collisions.", "difficulty": "easy"}, {"id": 3, "category": "Big-O", "question": "What is the space complexity of merge sort?", "answer": "O(n) - It requires additional space for merging.", "difficulty": "medium"}, {"id": 4, "category": "Big-O", "question": "What is the time complexity of quicksort in the worst case?", "answer": "O(n^2) - Occurs when the pivot is always the smallest or largest element.", "difficulty": "medium"}, {"id": 5, "category": "Big-O", "question": "What is the time complexity of traversing a binary tree?", "answer": "O(n) - Every node is visited once.", "difficulty": "easy"}, {"id": 6, "category": "Database", "question": "What is database sharding?", "answer": "Sharding is splitting a database into smaller, more manageable pieces called shards, each holding a subset of the data.", "difficulty": "medium"}, {"id": 7, "category": "Database", "question": "What is a CAP theorem?", "answer": "CAP theorem states that a distributed system can only guarantee two out of three: Consistency, Availability, and Partition Tolerance.", "difficulty": "medium"}, {"id": 8, "category": "Database", "question": "What is denormalization?", "answer": "Denormalization is the process of adding redundant data to improve read performance at the cost of write performance and storage.", "difficulty": "medium"}, {"id": 9, "category": "Database", "question": "What is an index in a database?", "answer": "An index is a data structure that improves the speed of data retrieval operations on a database table.", "difficulty": "easy"}, {"id": 10, "category": "Database", "question": "What is eventual consistency?", "answer": "Eventual consistency means that, given enough time, all updates will propagate through the system and all nodes will be consistent.", "difficulty": "medium"}, {"id": 11, "category": "Caching", "question": "What is a cache?", "answer": "A cache is a temporary storage layer that stores frequently accessed data for faster retrieval.", "difficulty": "easy"}, {"id": 12, "category": "Caching", "question": "What is cache invalidation?", "answer": "Cache invalidation is the process of removing or updating stale data from the cache.", "difficulty": "medium"}, {"id": 13, "category": "Caching", "question": "What is a cache eviction policy?", "answer": "A cache eviction policy determines which items to remove from the cache when it is full (e.g., LRU, LFU, FIFO).", "difficulty": "medium"}, {"id": 14, "category": "Caching", "question": "What is a write-through cache?", "answer": "In a write-through cache, data is written to both the cache and the underlying storage at the same time.", "difficulty": "medium"}, {"id": 15, "category": "Caching", "question": "What is a CDN?", "answer": "A Content Delivery Network (CDN) is a distributed network of servers that delivers content to users based on their geographic location.", "difficulty": "easy"}, {"id": 16, "category": "Networking", "question": "What is a load balancer?", "answer": "A load balancer distributes incoming network traffic across multiple servers to ensure reliability and performance.", "difficulty": "easy"}, {"id": 17, "category": "Networking", "question": "What is a reverse proxy?", "answer": "A reverse proxy is a server that sits in front of web servers and forwards client requests to those servers.", "difficulty": "medium"}, {"id": 18, "category": "Networking", "question": "What is DNS?", "answer": "The Domain Name System (DNS) translates human-readable domain names to IP addresses.", "difficulty": "easy"}, {"id": 19, "category": "Networking", "question": "What is a CDN edge server?", "answer": "A CDN edge server is a server located close to end users to deliver cached content quickly.", "difficulty": "medium"}, {"id": 20, "category": "Networking", "question": "What is SSL termination?", "answer": "SSL termination is the process of decrypting SSL-encrypted traffic at the load balancer before sending it to the backend servers.", "difficulty": "medium"}, {"id": 21, "category": "Scalability", "question": "What is horizontal scaling?", "answer": "Horizontal scaling means adding more machines to handle increased load.", "difficulty": "easy"}, {"id": 22, "category": "Scalability", "question": "What is vertical scaling?", "answer": "Vertical scaling means increasing the resources (CPU, RAM) of a single machine.", "difficulty": "easy"}, {"id": 23, "category": "Scalability", "question": "What is a stateless service?", "answer": "A stateless service does not store any client session data between requests.", "difficulty": "medium"}, {"id": 24, "category": "Scalability", "question": "What is a distributed system?", "answer": "A distributed system is a system with components located on different networked computers that communicate and coordinate their actions.", "difficulty": "medium"}, {"id": 25, "category": "Scalability", "question": "What is a message queue?", "answer": "A message queue is a form of asynchronous service-to-service communication used in serverless and microservices architectures.", "difficulty": "medium"}, {"id": 26, "category": "Reliability", "question": "What is failover?", "answer": "Failover is the process of switching to a standby system when the primary system fails.", "difficulty": "medium"}, {"id": 27, "category": "Reliability", "question": "What is redundancy?", "answer": "Redundancy is the duplication of critical components to increase reliability and availability.", "difficulty": "easy"}, {"id": 28, "category": "Reliability", "question": "What is a heartbeat in distributed systems?", "answer": "A heartbeat is a periodic signal sent to indicate normal operation or to synchronize parts of a system.", "difficulty": "medium"}, {"id": 29, "category": "Reliability", "question": "What is a quorum?", "answer": "A quorum is the minimum number of votes required to make a distributed transaction valid.", "difficulty": "medium"}, {"id": 30, "category": "Reliability", "question": "What is data replication?", "answer": "Data replication is the process of storing copies of data on multiple machines to ensure reliability and availability.", "difficulty": "easy"}, {"id": 31, "category": "Security", "question": "What is HTTPS?", "answer": "HTTPS is HTTP over SSL/TLS, providing encrypted communication and secure identification of a network web server.", "difficulty": "easy"}, {"id": 32, "category": "Security", "question": "What is rate limiting?", "answer": "Rate limiting restricts the number of requests a user can make to a service in a given time period.", "difficulty": "medium"}, {"id": 33, "category": "Security", "question": "What is <PERSON><PERSON><PERSON>?", "answer": "OAuth is an open standard for access delegation, commonly used for token-based authentication.", "difficulty": "medium"}, {"id": 34, "category": "Security", "question": "What is SQL injection?", "answer": "SQL injection is a code injection technique that might destroy your database by inserting malicious SQL statements.", "difficulty": "medium"}, {"id": 35, "category": "Security", "question": "What is XSS?", "answer": "Cross-Site Scripting (XSS) is a vulnerability that allows attackers to inject malicious scripts into web pages.", "difficulty": "medium"}, {"id": 36, "category": "API Design", "question": "What is REST?", "answer": "REST is an architectural style for designing networked applications using stateless communication and standard HTTP methods.", "difficulty": "easy"}, {"id": 37, "category": "API Design", "question": "What is idempotency in APIs?", "answer": "Idempotency means that making multiple identical requests has the same effect as making a single request.", "difficulty": "medium"}, {"id": 38, "category": "API Design", "question": "What is GraphQL?", "answer": "GraphQL is a query language for APIs that allows clients to request exactly the data they need.", "difficulty": "medium"}, {"id": 39, "category": "API Design", "question": "What is API versioning?", "answer": "API versioning is the practice of managing changes to an API by assigning version numbers to different releases.", "difficulty": "medium"}, {"id": 40, "category": "API Design", "question": "What is a web hook?", "answer": "A web hook is a way for an app to provide other applications with real-time information via HTTP callbacks.", "difficulty": "medium"}, {"id": 41, "category": "Monitoring", "question": "What is application monitoring?", "answer": "Application monitoring is the process of tracking the performance and availability of software applications.", "difficulty": "easy"}, {"id": 42, "category": "Monitoring", "question": "What is a metric?", "answer": "A metric is a quantifiable measure used to track and assess the status of a specific process.", "difficulty": "easy"}, {"id": 43, "category": "Monitoring", "question": "What is logging?", "answer": "Logging is the process of recording events and messages from software applications for troubleshooting and analysis.", "difficulty": "easy"}, {"id": 44, "category": "Monitoring", "question": "What is alerting?", "answer": "Alerting is the process of notifying responsible parties when a system metric crosses a predefined threshold.", "difficulty": "easy"}, {"id": 45, "category": "Monitoring", "question": "What is distributed tracing?", "answer": "Distributed tracing is a method to track requests as they flow through distributed systems.", "difficulty": "medium"}, {"id": 46, "category": "Storage", "question": "What is object storage?", "answer": "Object storage manages data as objects, as opposed to file systems or block storage.", "difficulty": "medium"}, {"id": 47, "category": "Storage", "question": "What is a data lake?", "answer": "A data lake is a centralized repository that allows you to store all your structured and unstructured data at any scale.", "difficulty": "medium"}, {"id": 48, "category": "Storage", "question": "What is RAID?", "answer": "RAID (Redundant Array of Independent Disks) is a data storage virtualization technology that combines multiple physical disk drives.", "difficulty": "medium"}, {"id": 49, "category": "Storage", "question": "What is a block storage?", "answer": "Block storage splits data into blocks and stores them as separate pieces, each with a unique identifier.", "difficulty": "medium"}, {"id": 50, "category": "Storage", "question": "What is a file system?", "answer": "A file system is a method and data structure that an operating system uses to control how data is stored and retrieved.", "difficulty": "easy"}]}