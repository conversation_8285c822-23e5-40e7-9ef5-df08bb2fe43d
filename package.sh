#!/bin/bash

# NerdAlert Extension Packaging Script
# This script creates a clean package for distribution

echo "🧠 NerdAlert Extension Packager"
echo "=============================="

# Create package directory
PACKAGE_DIR="NerdAlert-v1.0.0"
echo "📦 Creating package directory: $PACKAGE_DIR"
mkdir -p "$PACKAGE_DIR"

# Copy essential files
echo "📋 Copying extension files..."
cp manifest.json "$PACKAGE_DIR/"
cp newtab.html "$PACKAGE_DIR/"
cp popup.html "$PACKAGE_DIR/"

# Copy directories
echo "📁 Copying directories..."
cp -r css "$PACKAGE_DIR/"
cp -r js "$PACKAGE_DIR/"
cp -r data "$PACKAGE_DIR/"
cp -r icons "$PACKAGE_DIR/"

# Copy documentation
echo "📚 Copying documentation..."
cp README.md "$PACKAGE_DIR/"
cp INSTALLATION.md "$PACKAGE_DIR/"

# Remove development files from package
echo "🧹 Cleaning up development files..."
rm -f "$PACKAGE_DIR/test.html"
rm -f "$PACKAGE_DIR/create_icons.html"
rm -f "$PACKAGE_DIR/package.sh"

# Create zip file
echo "🗜️  Creating zip file..."
zip -r "$PACKAGE_DIR.zip" "$PACKAGE_DIR"

# Clean up temporary directory
echo "🧹 Cleaning up..."
rm -rf "$PACKAGE_DIR"

echo "✅ Package created: $PACKAGE_DIR.zip"
echo ""
echo "📦 Ready for distribution!"
echo "   - Upload to Chrome Web Store"
echo "   - Share with developers"
echo "   - Distribute for testing"
echo ""
echo "🚀 Installation instructions:"
echo "   1. Extract the zip file"
echo "   2. Open chrome://extensions/"
echo "   3. Enable Developer mode"
echo "   4. Click 'Load unpacked'"
echo "   5. Select the extracted folder"
echo ""
echo "Happy learning! 🎓"
